<?php

use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Auth\ForgotPasswordController;
use App\Http\Controllers\Auth\OtpController;
use App\Http\Controllers\Auth\SocialAuthController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\ESimAccessController;
use App\Http\Controllers\Notification\NotifyController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\Payment\PaymentController;
use Illuminate\Support\Facades\Route;

Route::get('/hello', function () {
    return ['message' => 'Hello World from API'];
});

Route::get('/countries', [CategoryController::class, 'getCountries']);
   // ->middleware('cache.headers:public;max_age=604800;immutable'); // TODO: CDN cache control

Route::get('/packages/{slug}',[CategoryController::class, 'getPackagesByCountry']);

Route::prefix('payment')->group(function () {
    Route::post('/success', [PaymentController::class, 'success'])->name('payment.success');
    Route::post('/cancel', [PaymentController::class, 'cancel'])->name('payment.cancel');
    Route::post('/callback', [PaymentController::class, 'callback'])->name('payment.callback');
});

Route::prefix('payment-stripe')->group(function () {
    Route::post('/status', [PaymentController::class, 'stripeHandle'])->name('stripe.payment.success');
});

// EsimAccess webhooks
Route::post('/notify-status', [NotifyController::class, 'handleNotification']);

// Auth Routes
Route::prefix('auth')->name('auth.')->group(function () {
    Route::get('{provider}/redirect', [SocialAuthController::class, 'redirect'])->name('redirect');
    Route::get('{provider}/callback', [SocialAuthController::class, 'callback'])->name('callback');
    Route::get('token/{authCode}', [SocialAuthController::class, 'exchangeToken'])->name('exchange');
});


Route::post('/login', [AuthController::class, 'login']);
Route::post('/forgot-password', [ForgotPasswordController::class, 'sendResetLink']);

// Rate limiting ayarları TODO: Rate limiting durumları gozden gecirilmeli ve special header key security
Route::middleware(['throttle:6,1'])->group(function () {
    Route::post('/auth/send-otp', [OtpController::class, 'send']);
});

Route::post('/auth/verify-otp', [OtpController::class, 'verify']);

// Protected Routes
Route::middleware('auth:sanctum')->group(function () {
    Route::get('/user', [AuthController::class, 'getUser']);
    Route::post('/checkout', [OrderController::class, 'createCheckoutOrder']);
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/my-esims', [ESimAccessController::class, 'getEsims']);
    Route::get('/esim-setup/{orderNo}', [ESimAccessController::class, 'getEsimSetupInfo']);
});
