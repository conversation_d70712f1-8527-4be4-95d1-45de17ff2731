<?php

namespace App\Http\Controllers;

use App\Constants\PaymentConstants;
use App\Models\Coupon;
use App\Models\Order;
use App\Models\Product;
use App\Services\StripeService;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class OrderController extends Controller
{
    private $stripeService;

    public function __construct(StripeService $stripeService)
    {
        $this->stripeService = $stripeService;
    }

    public function createCheckoutOrder(Request $request)
    {
        $user = auth()->user();
        $userId = $user->id;
        $email = $user->email;

        $validated = $request->validate([
            'product_code' => 'required|exists:products,code'
        ]);

        $product = Product::where('code', $validated['product_code'])->firstOrFail();
        ['coupon' => $coupon, 'discount' => $discount] = $this->applyCoupon($request, $product);

        $order = $this->createPendingOrder($product, $coupon, $discount, $userId,$email);

        //$paymentGatewayURL = app(PaymentController::class)->initiate($order);

        $clientSecret = $this->stripeService->createPaymentIntent($order);


        return response()->json([
            'order_uuid' => $order->uuid,
            'amount' => $order->amount,
            'currency' => $order->currency,
            'status' => $order->status,
            'clientSecret' => $clientSecret
        ], 201);
    }

    protected function applyCoupon(Request $request, Product $product): array
    {
        $coupon = null;
        $discount = 0;

        if (! $request->filled('coupon_code')) {
            return compact('coupon', 'discount');
        }

        $coupon = Coupon::where('code', $request->coupon_code)->first();

        if (! $coupon || ! $coupon->isUsable()) {
            abort(422, 'Geçersiz veya kullanılamaz kupon');
        }

        $usedCount = Order::where('coupon_code', $coupon->code)
            ->where('status', PaymentConstants::STATUS_PAID)
            ->count();

        if ($usedCount >= $coupon->usage_limit) {
            abort(422, 'Bu kupon artık kullanılamaz');
        }

        $discount = $coupon->type === 'percent'
            ? round($product->sale_price * ($coupon->amount / 100), 2)
            : min($coupon->amount, $product->sale_price);

        return compact('coupon', 'discount');
    }

    protected function createPendingOrder(Product $product, $coupon, float $discount, ?int $userId, string $email): Order
    {
        return Order::create([
            'product_id'    => $product->id,
            'user_id'       => $userId,
            'email'         => $email,
            'coupon_code'     => $coupon->code ?? null,
            'discount_amount' => $discount,
            'amount'          => $product->sale_price,
            'currency'      => $product->sale_price_currency,
            'product_name'  => $product->name,
            'product_code'  => $product->code,
            'status'        => PaymentConstants::STATUS_PENDING,
            'transaction_id' => $this->generateTransactionId(),
            'meta'          => [
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent()
            ],
        ]);
    }

    /**
     * Transaction ID oluştur
     */
    private function generateTransactionId(): string
    {
        return 'BS-' . Str::uuid();
    }
}
