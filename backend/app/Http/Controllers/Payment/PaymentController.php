<?php

namespace App\Http\Controllers\Payment;

use App\Constants\PaymentConstants;
use App\Contracts\PaymentServiceInterface;
use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Services\PayseraService;
use App\Services\StripeService;
use Illuminate\Http\Request;

class PaymentController extends Controller
{
    private $payseraService;
    private $stripeService;

    public function __construct(PayseraService $payseraService, StripeService $stripeService)
    {
        $this->payseraService = $payseraService;
        $this->stripeService = $stripeService;
    }


    /**
     * Handle payment success (Paysera)
     *
     * @param Request $request
     * @return mixed
     */
    public function success(Request $request)
    {
        return $this->payseraService->handleSuccess($request);
    }

    /**
     * Handle Stripe webhook
     *
     * @param Request $request
     * @return mixed
     */
    public function stripeHandle(Request $request)
    {
       return $this->stripeService->handleWebhook($request);
    }

    /**
     * Handle payment cancel (Paysera)
     *
     * @param Request $request
     * @return mixed
     */
    public function cancel(Request $request)
    {
        return $this->payseraService->handleCancel($request);
    }

    /**
     * Handle payment callback (Paysera)
     *
     * @param Request $request
     * @return mixed
     */
    public function callback(Request $request)
    {
        return $this->payseraService->handleWebhook($request);
    }


}
