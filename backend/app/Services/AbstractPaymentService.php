<?php

namespace App\Services;

use App\Constants\PaymentConstants;
use App\Contracts\PaymentServiceInterface;
use App\Http\Controllers\ESimAccessController;
use App\Models\Coupon;
use App\Models\Order;
use App\Models\OrderReference;
use Illuminate\Support\Str;

abstract class AbstractPaymentService implements PaymentServiceInterface
{

    /**
     * Mark payment as successful
     *
     * @param Order $order
     * @param OrderReference $reference
     * @return void
     */
    public function markPaymentAsSuccessful(Order $order, OrderReference $reference): void
    {
        $order->update(['status' => PaymentConstants::STATUS_PAID]);
        $reference->update(['status' => PaymentConstants::STATUS_COMPLETED]);
    }

    /**
     * Mark payment as failed
     *
     * @param Order $order
     * @param OrderReference $reference
     * @return void
     */
    public function markPaymentAsFailed(Order $order, OrderReference $reference): void
    {
        $order->update(['status' => PaymentConstants::STATUS_FAILED]);
        $reference->update(['status' => PaymentConstants::STATUS_FAILED]);
    }

    /**
     * Create order reference for tracking
     *
     * @param Order $order
     * @param string|null $referenceId
     * @return OrderReference
     */
    public function createOrderReference(Order $order, string $referenceId = null): OrderReference
    {
        return OrderReference::create([
            'order_id' => $order->id,
            'gateway' => $this->getGatewayName(),
            'reference_id' => $referenceId ?: $this->generateReferenceId(),
            'status' => PaymentConstants::STATUS_INITIATED,
        ]);
    }

    /**
     * Mark coupon as used if applicable
     *
     * @param Order $order
     * @return void
     */
    protected function markCouponAsUsed(Order $order): void
    {
        $coupon = Coupon::where('code', $order->coupon_code)->first();

        if ($coupon && $coupon->isUsable()) {
            $coupon->increment('used');
        }
    }

    /**
     * Process successful payment - common logic for all gateways
     *
     * @param Order $order
     * @param OrderReference $reference
     * @return void
     */
    protected function processSuccessfulPayment(Order $order, OrderReference $reference): void
    {
        $esimAccessController->createESimOrder($order);
        $this->markPaymentAsSuccessful($order, $reference);

        if ($order->coupon_code) {
            $this->markCouponAsUsed($order);
        }
    }

    /**
     * Generate reference ID for the payment gateway
     * Can be overridden by child classes if needed
     *
     * @return string
     */
    protected function generateReferenceId(): string
    {
        return Str::uuid();
    }

    /**
     * Get the gateway name for this payment service
     *
     * @return string
     */
    public function getGatewayName(): string
    {
        return static::GATEWAY_NAME;
    }
}
