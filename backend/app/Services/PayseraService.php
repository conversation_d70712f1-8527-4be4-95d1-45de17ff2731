<?php

namespace App\Services;

use App\Constants\PaymentConstants;
use App\Http\Controllers\ESimAccessController;
use App\Models\Order;
use App\Models\OrderReference;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use WebToPay;

class PayseraService extends AbstractPaymentService
{
    const GATEWAY_NAME = 'paysera';

    private $projectId;
    private $signPassword;

    public function __construct()
    {
        $this->projectId = config('services.paysera.project_id');
        $this->signPassword = config('services.paysera.sign_password');
    }

    /**
     * Create a payment for the given order
     *
     * @param Order $order
     * @return mixed
     * @throws \WebToPayException
     */
    public function createPayment(Order $order)
    {
        $reference = $this->createOrderReference($order);

        return $this->createPayseraPayment($order, $reference);
    }

    /**
     * Handle webhook/callback from payment gateway
     *
     * @param Request $request
     * @return mixed
     */
    public function handleWebhook(Request $request)
    {
        try {
            $response = WebToPay::validateAndParseData($request->all(), $this->projectId, $this->signPassword);

            $reference = OrderReference::where('reference_id', $response['orderid'])->firstOrFail();
            $order = $reference->order;

            if ($this->isPaymentSuccessful($response['status'])) {
                $this->processSuccessfulPayment($order, $reference);
            } else {
                $this->markPaymentAsFailed($order, $reference);
            }

            return response()->json(['status' => 'OK']);
        } catch (\Exception $e) {
            Log::error('Paysera callback error', [
                'message' => $e->getMessage(),
                'request' => $request->all(),
            ]);
            return response()->json(['status' => 'ERROR', 'message' => $e->getMessage()], 400);
        }
    }


    /**
     * Create Paysera payment request
     *
     * @param Order $order
     * @param OrderReference $reference
     * @return mixed
     * @throws \WebToPayException
     */
    protected function createPayseraPayment(Order $order, OrderReference $reference)
    {
        $request = [
            'projectid'     => $this->projectId,
            'sign_password' => $this->signPassword,
            'orderid'       => $reference->reference_id,
            'amount'        => $order->amount * 100, // Paysera expects amount in cents
            'currency'      => $order->currency,
            'country'       => PaymentConstants::COUNTRY_LITHUANIA,
            'accepturl'     => route('payment.success'),
            'cancelurl'     => route('payment.cancel'),
            'callbackurl'   => route('payment.callback'),
            'test'          => config('app.env') !== 'production',
            'p_email'       => $order->email,
            'p_firstname'   => $order->email,
            'p_lastname'    => $order->email,
        ];

        return WebToPay::redirectToPayment($request);
    }

    /**
     * Check if payment is successful
     *
     * @param $status
     * @return bool
     */
    protected function isPaymentSuccessful($status): bool
    {
        return $status === PaymentConstants::PAYSERA_SUCCESS_STATUS;
    }

    /**
     * Generate reference ID with Paysera prefix
     *
     * @return string
     */
    protected function generateReferenceId(): string
    {
        return PaymentConstants::REFERENCE_PREFIX . parent::generateReferenceId();
    }



    /**
     * Handle success callback
     *
     * @param Request $request
     * @return mixed
     */
    public function handleSuccess(Request $request)
    {
        try {
            $response = WebToPay::validateAndParseData($request->all(), $this->projectId, $this->signPassword);

            $reference = OrderReference::where('reference_id', $response['orderid'])->firstOrFail();
            $order = $reference->order;

            $this->markPaymentAsSuccessful($order, $reference);

            return redirect()->route('payment.success.page');
        } catch (\Exception $e) {
            return redirect()->route('payment.error.page');
        }
    }

    /**
     * Handle cancel callback
     *
     * @param Request $request
     * @return mixed
     */
    public function handleCancel(Request $request)
    {
        try {
            $response = WebToPay::validateAndParseData($request->all(), $this->projectId, $this->signPassword);

            $reference = OrderReference::where('reference_id', $response['orderid'])->firstOrFail();
            $order = $reference->order;

            $order->update(['status' => PaymentConstants::STATUS_CANCELLED]);
            $reference->update(['status' => PaymentConstants::STATUS_CANCELLED]);

            return redirect()->route('payment.cancel.page');
        } catch (\Exception $e) {
            return redirect()->route('payment.error.page');
        }
    }
}
