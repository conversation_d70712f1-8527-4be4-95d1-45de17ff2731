<?php

namespace App\Services;


use App\Models\Order;
use App\Models\OrderReference;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Stripe\Exception\ApiErrorException;
use Stripe\PaymentIntent;
use Stripe\Stripe;
use Stripe\Webhook;

class StripeService extends AbstractPaymentService
{

    const GATEWAY_NAME = 'stripe';

    public function __construct()
    {
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    /**
     * Create a payment for the given order
     *
     * @param Order $order
     * @return mixed
     * @throws ApiErrorException
     */
    public function createPayment(Order $order)
    {
        return $this->createPaymentIntent($order);
    }

    /**
     * @throws ApiErrorException
     */
    public function createPaymentIntent(Order $order): ?string
    {
        $paymentIntent = PaymentIntent::create([
            'amount' => $order->amount * 100,
            'currency' => $order->currency,
            'automatic_payment_methods' => ['enabled' => true],
            'metadata' => [
                'order_id' => $order->id,
                'product name' => $order->product->name,
                'receipt_email' => $order->user->email,
            ]
        ]);

        $reference = $this->createOrderReference($order, $paymentIntent->id);

        return $paymentIntent->client_secret;
    }

    public function handleWebhook(Request $request)
    {
        $payload = $request->getContent();
        $sig_header = $request->header('Stripe-Signature');
        $endpoint_secret = config('services.stripe.webhook_secret');

        try {
            $event = Webhook::constructEvent($payload, $sig_header, $endpoint_secret);
        } catch (\Exception $e) {
            Log::error("Stripe webhook doğrulanamadı: " . $e->getMessage());
            return response()->json(['error' => 'invalid signature'], 400);
        }

        if ($event->type === 'payment_intent.succeeded') {
            $intent = $event->data->object;

            $orderId = $intent->metadata->order_id ?? null;

            if (!$orderId) {
                Log::emergency("Stripe webhook: order_id metadata eksik.");
                return response()->json(['error' => 'missing order_id'], 400);
            }

            $reference = OrderReference::where('reference_id', $intent->id)->first();
            $order = $reference->order ?? null;

            if (!$order) {
                Log::emergency("Sipariş bulunamadı: " . $orderId);
                return response()->json(['error' => 'order not found'], 404);
            }

            $this->processSuccessfulPayment($order, $reference);
        }

        Log::error('Stripe Webhook unhandled: ' . $request->type, [
            'request' => json_encode($request)
        ]);

        return response('Unhandled webhook Handled', 400);
    }


}
