<?php

namespace App\Contracts;

use App\Models\Order;
use App\Models\OrderReference;
use Illuminate\Http\Request;

interface PaymentServiceInterface
{
    /**
     * Create a payment for the given order
     *
     * @param Order $order
     * @return mixed
     */
    public function createPayment(Order $order);

    /**
     * Handle webhook/callback from payment gateway
     *
     * @param Request $request
     * @return mixed
     */
    public function handleWebhook(Request $request);

    /**
     * Get the gateway name for this payment service
     *
     * @return string
     */
    public function getGatewayName(): string;

    /**
     * Mark payment as successful
     *
     * @param Order $order
     * @param OrderReference $reference
     * @return void
     */
    public function markPaymentAsSuccessful(Order $order, OrderReference $reference): void;

    /**
     * Mark payment as failed
     *
     * @param Order $order
     * @param OrderReference $reference
     * @return void
     */
    public function markPaymentAsFailed(Order $order, OrderReference $reference): void;

    /**
     * Create order reference for tracking
     *
     * @param Order $order
     * @param string|null $referenceId
     * @return OrderReference
     */
    public function createOrderReference(Order $order, string $referenceId = null): OrderReference;
}
