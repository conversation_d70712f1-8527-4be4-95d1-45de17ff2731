'use client';

import React, {useState, forwardRef, useImperativeHandle} from 'react';
import {Elements, useStripe, useElements} from '@stripe/react-stripe-js';
import {loadStripe} from '@stripe/stripe-js';
import {PaymentElement} from '@stripe/react-stripe-js';
import {useTranslation} from 'react-i18next';
import type {StripeElementsOptions, StripePaymentElementOptions} from '@stripe/stripe-js';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

interface StripePaymentElementProps {
    clientSecret: string | null;
    onPaymentSuccess: () => void;
    onPaymentError: (error: any) => void;
    onProcessingChange?: (isProcessing: boolean) => void;
    onPaymentMethodChange?: (paymentMethodType: string | null) => void;
}

export interface PaymentFormRef {
    submitForm: () => void;
    isProcessing: boolean;
}

interface PaymentFormProps extends Omit<StripePaymentElementProps, 'clientSecret'> {
    ref?: React.Ref<PaymentFormRef>;
}

const PaymentForm = forwardRef<PaymentFormRef, PaymentFormProps>(({
    onPaymentSuccess,
    onPaymentError,
    onProcessingChange,
    onPaymentMethodChange
}, ref) => {
    const stripe = useStripe();
    const elements = useElements();
    const {t} = useTranslation();
    const [isProcessing, setIsProcessing] = useState(false);
    const [errorMessage, setErrorMessage] = useState<string | null>(null);

    const handleSubmit = async (e?: React.FormEvent) => {
        if (e) {
            e.preventDefault();
        }

        if (!stripe || !elements) {
            return;
        }

        setIsProcessing(true);
        onProcessingChange?.(true);
        setErrorMessage(null);

        const {error} = await stripe.confirmPayment({
            elements,
            confirmParams: {
                return_url: `${window.location.origin}/checkout/payment`,
            },
        });

        if (error) {
            setErrorMessage(error.message || t('checkout.payment.genericError'));
            onPaymentError(error);
        } else {
            onPaymentSuccess();
        }

        setIsProcessing(false);
        onProcessingChange?.(false);
    };

    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
        submitForm: () => handleSubmit(),
        isProcessing
    }));

    const handlePaymentElementChange = (event: any) => {
        // Stripe PaymentElement onChange event provides event.value.type
        if (event.value && event.value.type && onPaymentMethodChange) {
            onPaymentMethodChange(event.value.type);
        }
    };

    const options: StripePaymentElementOptions = {
        layout: {
            type: 'accordion',
            radios: true,
            defaultCollapsed: true,
            spacedAccordionItems: true,
        },
    };

    return (
        <form onSubmit={handleSubmit}>
            <PaymentElement onChange={handlePaymentElementChange}
                            options={options}/>
            {errorMessage && (
                <div className="mt-4 p-3 bg-red-50 border border-red-200 text-red-600 rounded-2xl">
                    {errorMessage}
                </div>
            )}
        </form>
    );
});

PaymentForm.displayName = 'PaymentForm';

const StripePaymentElement = forwardRef<PaymentFormRef, StripePaymentElementProps>(({
    clientSecret,
    onPaymentSuccess,
    onPaymentError,
    onProcessingChange,
    onPaymentMethodChange
}, ref) => {
    const {t} = useTranslation();

    if (!clientSecret) {
        return null;
    }

    const options: StripeElementsOptions = {
        clientSecret,
        appearance: {
            theme: 'stripe',
            variables: {
                colorPrimary: '#000000',
                colorText: '#000000',
                borderRadius: '16px',
            },
            rules: {
                '.AccordionItem': {
                    border: '2px solid #d1d5db',
                },
                '.AccordionItem--selected': {
                    borderColor: '#fbbf24', // yellow-400
                },
                '.AccordionItem:hover': {
                    borderColor: '#fbdd8c',
                },
            }
        }
    };

    return (
        <Elements stripe={stripePromise} options={options}>
            <PaymentForm
                ref={ref}
                onPaymentSuccess={onPaymentSuccess}
                onPaymentError={onPaymentError}
                onProcessingChange={onProcessingChange}
                onPaymentMethodChange={onPaymentMethodChange}
            />
        </Elements>
    );
});

StripePaymentElement.displayName = 'StripePaymentElement';

export default StripePaymentElement;
