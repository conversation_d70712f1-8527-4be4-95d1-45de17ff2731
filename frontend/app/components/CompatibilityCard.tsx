'use client';

import React from 'react';
import { useTranslation } from 'react-i18next';
import { cdn } from '@/lib/cdn';

interface CompatibilityCardProps {
  onCheckClick: () => void;
  className?: string;
}

const CompatibilityCard: React.FC<CompatibilityCardProps> = ({
  onCheckClick,
  className = ''
}) => {
  const { t } = useTranslation();

  return (
    <div className={`compatibility-card ${className}`}>
      {/* Mobil görünüm */}
      <div className="flex flex-col items-end text-right relative md:hidden">
        <h2 className="text-xl font-bold mb-2" style={{ color: "#0e0f0c" }}>
          {t('home.isYourPhoneCompatible', 'Telefonun eSIM uyumlu mu?')}
        </h2>
        <p className="mb-4" style={{ color: "#979a96" }}>
          {t('home.quicklyCheckYourDevice', 'Cihazınızı hızlıca kontrol edin')}
        </p>
        <button
          className="bg-yellow-400 hover:bg-yellow-500 text-black font-medium py-2 px-6 rounded-full"
          onClick={onCheckClick}
        >
          {t('home.checkCompatibility', 'Kontrol Et')}
        </button>
      </div>

      {/* Masaüstü görünüm */}
      <div className="hidden md:block md:flex-1 md:pr-4 relative">
        <h2 className="text-xl font-bold mb-2" style={{ color: "#0e0f0c" }}>
          {t('home.isYourPhoneCompatible', 'Telefonun eSIM uyumlu mu?')}
        </h2>
        <p className="mb-3 text-base max-w-md" style={{ color: "#979a96" }}>
          {t('home.quicklyCheckYourDevice', 'Cihazınızı hızlıca kontrol edin')}
        </p>
        <button
          className="bg-yellow-400 hover:bg-yellow-500 text-black font-medium py-2 px-6 rounded-full"
          onClick={onCheckClick}
        >
          {t('home.checkCompatibility', 'Kontrol Et')}
        </button>
      </div>

      {/* Mobil görünüm için resim */}
      <img
        src={cdn('/esim-ready-placeholder.webp')}
        alt="eSIM"
        className="absolute bottom-0 left-0 w-40 h-auto object-contain opacity-90 md:hidden"
        style={{
          transform: "rotate(-15deg) translateX(-23px) translateY(38px)",
          maxWidth: "50%"
        }}
      />

      {/* Masaüstü görünüm için resim */}
      <div className="hidden md:block md:flex-shrink-0 relative">
      <img
          src={cdn('/esim-ready-placeholder.webp')}
          alt="eSIM test"
          className="w-48 h-auto object-contain"
          style={{
              transform: "rotate(-15deg) translateX(-23px) translateY(38px)",
          }}
        />
      </div>
    </div>
  );
};

export default CompatibilityCard;
