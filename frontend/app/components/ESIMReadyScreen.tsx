'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { cdn } from "@/lib/cdn";
import confetti from 'canvas-confetti';

const ESIMReadyScreen = (): React.ReactNode => {
    const router = useRouter();

    React.useEffect(() => {
        var count = 200;
        var defaults = {
            origin: { y: 0.7 }
        };

        function fire(particleRatio: number, opts: any) {
            confetti({
                ...defaults,
                ...opts,
                particleCount: Math.floor(count * particleRatio)
            });
        }

        fire(0.25, {
            spread: 26,
            startVelocity: 55,
        });
        fire(0.2, {
            spread: 60,
        });
        fire(0.35, {
            spread: 100,
            decay: 0.91,
            scalar: 0.8
        });
        fire(0.1, {
            spread: 120,
            startVelocity: 25,
            decay: 0.92,
            scalar: 1.2
        });
        fire(0.1, {
            spread: 120,
            startVelocity: 45,
        });
    }, []);

    const handleInstallClick = () => {
        router.push('/esims');
    };

    return (
        <div className="fixed inset-0 z-[100] flex flex-col items-center justify-between bg-gray-900 text-white px-6 py-12 overflow-hidden">
            {/* Top area with image */}
            <div className="flex-1 flex flex-col items-center justify-center w-full relative z-[10]">
                {/* Placeholder image or fallback */}
                <div className="mb-16 relative w-56 h-56 flex items-center justify-center">
                    <img
                        src={cdn('/esim-ready-placeholder.webp')}
                        alt="eSIM Ready"
                        width={460}
                        height={460}
                        className="object-contain"
                    />
                </div>

                {/* Success text */}
                <h1 className="text-5xl font-bold mb-8 text-center text-yellow-400 tracking-wide">
                    ALL SET!
                </h1>

                {/* Description text */}
                <p className="text-xl text-center mb-2">
                    Your eSIM is ready. Enjoy your journey!
                </p>
            </div>

            {/* Button */}
            <div className="w-full mt-8 relative z-[10]">
                <button
                    onClick={handleInstallClick}
                    className="w-full bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-semibold py-4 px-4 rounded-full text-xl"
                >
                    Install your eSIM
                </button>
            </div>
        </div>
    );
};

export default ESIMReadyScreen;
