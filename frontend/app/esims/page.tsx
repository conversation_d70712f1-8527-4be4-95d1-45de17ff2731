'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import DataUsage from '../components/DataUsage';
import CountryFlag from '../components/CountryFlag';
import ESIMCard from '../components/ESIMCard';
import api, { isAuthenticated } from '@/lib/api';
import {cdn} from "@/lib/cdn";
import { useTranslation, Trans } from 'react-i18next';
import { FiChevronDown, FiChevronUp } from 'react-icons/fi';
import { useUser } from '@/context/UserContext';

interface ESIMOrder {
    id: number;
    country: string; // TODO: countryName diyebiliriz anlık çevriliyor.
    countryCode: string;
    dataRemaining: number;
    totalData: number;
    daysRemaining: number;
    hoursRemaining: number;
    isActive: boolean;
    purchaseDate: string;
    status: string;
    plan?: string;
    type?: string;
    duration?: string;
    orderNo?: string;
}

interface ESimSetupData {
    qrCodeUrl?: string;
    activationCode?: string;
    iccid?: string;
    imsi?: string;
    msisdn?: string;
    apn?: string;
}

const ESIMsPage = () => {
    const { t } = useTranslation();
    const { user, loading: userLoading } = useUser();
    const [esimOrders, setEsimOrders] = React.useState<ESIMOrder[]>([]);
    const [loading, setLoading] = React.useState(true);
    const [error, setError] = React.useState<string | null>(null);
    const [selectedEsim, setSelectedEsim] = React.useState<number | null>(null);
    const [setupData, setSetupData] = React.useState<ESimSetupData | null>(null);
    const [setupLoading, setSetupLoading] = React.useState(false);
    const [openAccordions, setOpenAccordions] = React.useState<{ [key: string]: boolean }>({
        setupInstructions: false,
        esimDetails: false,
        setupQrCode: true
    });

    // API'den eSIM verilerini çek - sadece bileşen yüklendiğinde çalışır
    const [rawEsimData, setRawEsimData] = React.useState<ESIMOrder[]>([]);

    React.useEffect(() => {
        const fetchESIMUsage = async () => {
            try {
                // Only fetch eSIMs if user is authenticated
                if (isAuthenticated()) {
                    setLoading(true);
                    const response = await api.get('/api/my-esims');
                    setRawEsimData(response.data);
                    setError(null);
                } else {
                    // If not authenticated, set loading to false
                    setLoading(false);
                }
            } catch (err) {
                setError(t('esims.errorFetchingData', 'eSIM kullanım bilgileri alınırken bir hata oluştu.'));
                console.error(err);
            } finally {
                setLoading(false);
            }
        };

        fetchESIMUsage();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []); // Sadece bileşen yüklendiğinde çalışır

    // Dil değiştiğinde ülke isimlerini çevir
    React.useEffect(() => {
        if (rawEsimData.length > 0) {
            const translatedData = rawEsimData.map((esim: ESIMOrder) => ({
                ...esim,
                country: t(`regions:countries.${esim.countryCode.toUpperCase()}.name`, esim.countryCode.toUpperCase())
            }));
            setEsimOrders(translatedData);
        }
    }, [t, rawEsimData]);

    // Seçilen eSIM'in detaylarını gösterme
    const showDetails = (id: number) => {
        setSelectedEsim(id);
        const selectedEsim = esimOrders.find((esim: ESIMOrder) => esim.id === id);
        if (selectedEsim && selectedEsim.orderNo) {
            fetchEsimSetupData(selectedEsim.orderNo);
        }
    };

    // eSIM kurulum verilerini çek
    const fetchEsimSetupData = async (orderNo: string) => {
        try {
            setSetupLoading(true);
            const response = await api.get(`/api/esim-setup/${orderNo}`);
            setSetupData(response.data);
        } catch (err) {
            console.error('eSIM setup data fetch error:', err);
            setSetupData(null);
        } finally {
            setSetupLoading(false);
        }
    };

    // Akordiyon açma/kapama
    const toggleAccordion = (accordionId: string) => {
        setOpenAccordions((prev) => ({
            ...prev,
            [accordionId]: !prev[accordionId]
        }));
    };

    // Detay görünümünden listeye dönme
    const backToList = () => {
        setSelectedEsim(null);
    };

    // Seçilen eSIM'i bul
    const selectedEsimDetails = esimOrders.find((esim: ESIMOrder) => esim.id === selectedEsim);

    if (loading) {
        return (
            <div className="app-container bg-gray-100 pt-6 pb-20 flex items-center justify-center min-h-[300px]">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400 mx-auto"></div>
                    <p className="mt-4 text-gray-600">{t('common.loading', 'Yükleniyor...')}</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="app-container bg-gray-100 pt-6 pb-20">
                <div className="p-6 flex flex-col items-center justify-center min-h-[300px] text-center">
                    <p className="text-red-500 mb-4">{error}</p>
                    <button
                        onClick={() => window.location.reload()}
                        className="btn-primary"
                    >
                        {t('esims.tryAgain', 'Tekrar Dene')}
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="app-container pb-20 pt-6">
            {selectedEsim === null && (
                <header className="mb-6">
                    <h1 className="text-3xl font-bold text-left text-black">{t('common.esims', "eSIM'lerim")}</h1>
                </header>
            )}
            {!isAuthenticated() ? (
                <div className="p-6 flex flex-col items-center justify-center min-h-[300px] text-center">
                    <div className="mb-8 relative" style={{ minHeight: '350px', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                        {/* Signal-like circles behind the image */}
                        <div className="absolute inset-0 flex items-center justify-center pointer-events-none" style={{ width: '100%', height: '100%' }}>
                            {/* Create 6 circles of increasing size for a signal-like effect */}
                            {[...Array(6)].map((_, i) => (
                                <div
                                    key={i}
                                    className="absolute rounded-full"
                                    style={{
                                        width: `${(i + 1) * 60}px`,
                                        height: `${(i + 1) * 60}px`,
                                        opacity: 0.5 - (i * 0.06),
                                        border: `${2 + (i * 0.6)}px solid #4aaf7f`,
                                        backgroundColor: i === 0 ? 'rgba(74, 175, 127, 0.1)' : 'transparent',
                                        position: 'absolute',
                                        top: '50%',
                                        left: '50%',
                                        transform: 'translate(-50%, -50%)',
                                        zIndex: 1
                                    }}
                                />
                            ))}
                        </div>
                        <img
                            src={cdn('/esim-ready-placeholder.webp')}
                            alt="eSIM"
                            width={230}
                            height={230}
                            className="object-contain transform rotate-6 relative z-10"
                            style={{ position: 'relative', filter: 'drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1))' }}
                        />
                    </div>
                    <p className="text-gray-600 text-center">
                        <Trans
                            i18nKey="esims.signInMessage"
                            defaults="<0>{{signInText}}</0> {{viewExistingEsims}}"
                            values={{
                                signInText: t('esims.signInText'),
                                viewExistingEsims: t('esims.viewExistingEsims')
                            }}
                            components={[<Link href="/auth/signin" className="text-black font-medium border-b border-black pb-0.5" />]}
                        />
                    </p>
                </div>
            ) : esimOrders.length === 0 ? (
                <div className="p-6 flex flex-col items-center justify-center min-h-[300px] text-center">
                    <div className="mb-8 relative" style={{ minHeight: '350px', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                        {/* Signal-like circles behind the image */}
                        <div className="absolute inset-0 flex items-center justify-center pointer-events-none" style={{ width: '100%', height: '100%' }}>
                            {/* Create 6 circles of increasing size for a signal-like effect */}
                            {[...Array(6)].map((_, i) => (
                                <div
                                    key={i}
                                    className="absolute rounded-full"
                                    style={{
                                        width: `${(i + 1) * 60}px`,
                                        height: `${(i + 1) * 60}px`,
                                        opacity: 0.5 - (i * 0.06),
                                        border: `${2 + (i * 0.6)}px solid #4aaf7f`,
                                        backgroundColor: i === 0 ? 'rgba(74, 175, 127, 0.1)' : 'transparent',
                                        position: 'absolute',
                                        top: '50%',
                                        left: '50%',
                                        transform: 'translate(-50%, -50%)',
                                        zIndex: 1
                                    }}
                                />
                            ))}
                        </div>
                        <img
                            src={cdn('/esim-ready-placeholder.webp')}
                            alt="eSIM Ready"
                            width={230}
                            height={230}
                            className="object-contain transform rotate-6 relative z-10"
                            style={{ position: 'relative', filter: 'drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1))' }}
                        />
                    </div>
                    <p className="text-gray-500 mb-6">{t('esims.noActiveEsims', 'Henüz aktif bir eSIM\'iniz bulunmuyor.')}</p>
                    <Link href="/" className="w-full max-w-xs">
                        <button className="border-2 border-green-800 text-green-800 hover:border-yellow-500  font-medium w-full py-3 px-8 rounded-full flex items-center justify-center">
                            {t('esims.buyNewEsim', 'Yeni eSIM Satın Al')}
                        </button>
                    </Link>
                </div>
            ) : selectedEsim === null ? (
                // eSIM Listesi
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {esimOrders.map((esim: ESIMOrder) => (
                        <ESIMCard
                            key={esim.id}
                            id={esim.id}
                            country={esim.country}
                            countryCode={esim.countryCode}
                            dataRemaining={esim.dataRemaining}
                            totalData={esim.totalData}
                            daysRemaining={esim.daysRemaining}
                            isActive={esim.isActive}
                            purchaseDate={esim.purchaseDate}
                            status={esim.status}
                            orderNo={esim.orderNo}
                            onClick={showDetails}
                        />
                    ))}
                </div>
            ) : (
                // eSIM Detay Görünümü
                <div>
                    <button
                        onClick={backToList}
                        className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-200  transition-colors mb-4 ml-3"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#4B5563" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M19 12H5"></path>
                            <polyline points="12 19 5 12 12 5"></polyline>
                        </svg>
                    </button>

                    {selectedEsimDetails && (
                        <div className="rounded-xl overflow-hidden">
                            <div className="border-b border-gray-100 px-3">
                                <div className="flex items-center mb-3 pt-3">
                                    <div className="ml-1 mr-4 w-10 h-10">
                                        <CountryFlag
                                            countryCode={selectedEsimDetails.countryCode}
                                            width={40}
                                            alt={selectedEsimDetails.country}
                                        />
                                    </div>
                                    <div>
                                        <h3 className="font-semibold text-xl text-gray-700">{selectedEsimDetails.country}</h3>
                                        <p className="text-sm text-gray-500">{t('esims.purchasedOn', 'Satın alındı: {{date}}', { date: selectedEsimDetails.purchaseDate })}</p>
                                    </div>
                                </div>

                                {selectedEsimDetails.isActive && (
                                    <DataUsage
                                        dataRemaining={selectedEsimDetails.dataRemaining}
                                        totalData={selectedEsimDetails.totalData}
                                        daysRemaining={selectedEsimDetails.daysRemaining}
                                        hoursRemaining={selectedEsimDetails.hoursRemaining}
                                        className="mb-4"
                                    />
                                )}

                                {/* Plan, Tür ve Süre Bilgileri */}
                                <div className="border-t border-gray-100 pt-5 px-3">
                                    <div className="flex items-center justify-between">
                                        {/* Plan Bilgisi */}
                                        <div className="flex items-center">
                                            <div className="w-10 h-10 rounded-full bg-white border border-gray-200 flex items-center justify-center mr-2">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                    <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                                                    <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                                                    <line x1="12" y1="22.08" x2="12" y2="12"></line>
                                                </svg>
                                            </div>
                                            <div>
                                                <p className="text-gray-600 text-xs">Plan</p>
                                                <p className="font-semibold text-gray-800 text-sm">{selectedEsimDetails.plan || `${selectedEsimDetails.totalData}GB`}</p>
                                            </div>
                                        </div>

                                        {/* Tür Bilgisi */}
                                        <div className="flex items-center">
                                            <div className="w-10 h-10 rounded-full bg-white border border-gray-200 flex items-center justify-center mr-2">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                    <rect x="4" y="4" width="16" height="16" rx="2" ry="2"></rect>
                                                    <path d="M4 12h16"></path>
                                                    <path d="M12 4v16"></path>
                                                </svg>
                                            </div>
                                            <div>
                                                <p className="text-gray-600 text-xs">Tür</p>
                                                <p className="font-semibold text-gray-800 text-sm">{selectedEsimDetails.type || 'Sadece Veri'}</p>
                                            </div>
                                        </div>

                                        {/* Süre Bilgisi */}
                                        <div className="flex items-center">
                                            <div className="w-10 h-10 rounded-full bg-white border border-gray-200 flex items-center justify-center mr-2">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                    <circle cx="12" cy="12" r="10"></circle>
                                                    <polyline points="12 6 12 12 16 14"></polyline>
                                                </svg>
                                            </div>
                                            <div>
                                                <p className="text-gray-600 text-xs">Süre</p>
                                                <p className="font-semibold text-gray-800 text-sm">{selectedEsimDetails.duration || '30 gün'}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Kurulum Yönergeleri ve QR Kod - Akordiyon */}
                                <div className="mb-6 mt-6 space-y-4">
                                    {/* Kurulum Yönergeleri Akordiyon */}
                                    <div className="bg-white rounded-xl border border-gray-100 overflow-hidden">
                                        <button
                                            onClick={() => toggleAccordion('setupInstructions')}
                                            className="w-full p-4 flex justify-between items-center transition-colors"
                                        >
                                            <h4 className="font-semibold text-lg text-gray-700 text-left">{t('esims.setupInstructions', 'Kurulum Yönergeleri')}</h4>
                                            <div className={`transform transition-transform duration-300 ${openAccordions['setupInstructions'] ? 'rotate-180' : 'rotate-0'}`}>
                                                <FiChevronDown className="h-5 w-5 text-gray-500" />
                                            </div>
                                        </button>
                                        <div
                                            className={`overflow-hidden transition-all duration-500 ease-in-out ${openAccordions['setupInstructions'] ? 'max-h-[500px]' : 'max-h-0'}`}
                                        >
                                            <div className="p-4 border-t border-gray-100">
                                                <ol className="list-decimal pl-5 space-y-2">
                                                    <li>{t('esims.setupStep1', 'Telefonunuzun ayarlar menüsüne gidin')}</li>
                                                    <li>{t('esims.setupStep2', 'Hücresel veya Mobil Veri seçeneğini bulun')}</li>
                                                    <li>{t('esims.setupStep3', 'eSIM veya Hücresel Plan Ekle\'ye tıklayın')}</li>
                                                    <li>{t('esims.setupStep4', 'Aşağıdaki QR kodu tarayın veya kurulum kodunu kullanın')}</li>
                                                </ol>

                                                {setupLoading ? (
                                                    <div className="flex justify-center items-center py-4">
                                                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-yellow-400"></div>
                                                        <span className="ml-2 text-gray-600">{t('common.loading', 'Yükleniyor...')}</span>
                                                    </div>
                                                ) : (
                                                    <div>
                                                        {!setupData || (!setupData.activationCode && !setupData.qrCodeUrl) ? (
                                                            <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-4 mb-4">
                                                                <div className="flex items-center">
                                                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                                                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                                                    </svg>
                                                                    <p className="text-sm text-yellow-700">{t('esims.setupDataNotAvailable', 'eSIM kurulum bilgileri henüz hazır değil. Lütfen daha sonra tekrar deneyin.')}</p>
                                                                </div>
                                                            </div>
                                                        ) : (
                                                            <div className="flex flex-col items-center bg-white rounded-xl p-4 border border-gray-200 mb-4 mt-4">
                                                                {/* QR Code */}
                                                                <div className="border border-gray-300 rounded-xl p-1 w-48 h-48 flex items-center justify-center mb-4">
                                                                    {setupData?.qrCodeUrl ? (
                                                                        <Image
                                                                            src={setupData.qrCodeUrl}
                                                                            alt="eSIM QR Code"
                                                                            width={180}
                                                                            height={180}
                                                                            className="object-contain"
                                                                        />
                                                                    ) : (
                                                                        <div className="w-full h-full bg-white flex items-center justify-center">
                                                                            <p className="text-sm text-gray-500 text-center p-2">{t('esims.qrCodeNotAvailable', 'QR kod henüz hazır değil')}</p>
                                                                        </div>
                                                                    )}
                                                                </div>

                                                                {/* Setup Code */}
                                                                <div className="w-full">
                                                                    <p className="font-medium mb-2 text-center">{t('esims.setupCode', 'Kurulum Kodu')}</p>
                                                                    <div className="flex items-center relative">
                                                                        <div className="bg-bg-white p-2 rounded font-mono text-sm overflow-hidden text-ellipsis w-full">
                                                                            {setupData?.activationCode || t('esims.codeNotAvailable', 'Kod henüz hazır değil')}
                                                                        </div>
                                                                        {setupData?.activationCode && (
                                                                            <button
                                                                                className="ml-2 p-2 text-blue-600 hover:text-blue-800 transition-colors flex-shrink-0"
                                                                                onClick={(e) => {
                                                                                    if (setupData?.activationCode) {
                                                                                        navigator.clipboard.writeText(setupData.activationCode);

                                                                                        // Show toast notification
                                                                                        const toast = document.createElement('div');
                                                                                        toast.className = 'absolute -top-10 left-0 right-0 bg-green-100 text-green-800 text-sm py-1 px-3 rounded-md text-center transition-opacity duration-300 opacity-0';
                                                                                        toast.textContent = t('esims.codeCopied', 'Kod kopyalandı!');
                                                                                        document.querySelector('.flex.items-center.relative')?.appendChild(toast);

                                                                                        // Animate in
                                                                                        setTimeout(() => {
                                                                                            toast.style.opacity = '1';
                                                                                        }, 10);

                                                                                        // Remove after delay
                                                                                        setTimeout(() => {
                                                                                            toast.style.opacity = '0';
                                                                                            setTimeout(() => {
                                                                                                toast.remove();
                                                                                            }, 300);
                                                                                        }, 2000);
                                                                                    }
                                                                                }}
                                                                                title={t('esims.copyCode', 'Kodu kopyala')}
                                                                            >
                                                                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                                                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                                                                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                                                                </svg>
                                                                            </button>
                                                                        )}
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        )}
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    </div>

                                    {/* eSIM Teknik Bilgileri Akordiyon */}
                                    <div className="bg-white rounded-xl border border-gray-100 overflow-hidden">
                                        <button
                                            onClick={() => toggleAccordion('esimDetails')}
                                            className="w-full p-4 flex justify-between items-center hover:bg-gray-50 transition-colors"
                                        >
                                            <h4 className="font-semibold text-lg text-gray-700 text-left">{t('esims.technicalDetails', 'eSIM Teknik Bilgileri')}</h4>
                                            <div className={`transform transition-transform duration-300 ${openAccordions['esimDetails'] ? 'rotate-180' : 'rotate-0'}`}>
                                                <FiChevronDown className="h-5 w-5 text-gray-500" />
                                            </div>
                                        </button>
                                        <div
                                            className={`overflow-hidden transition-all duration-500 ease-in-out ${openAccordions['esimDetails'] ? 'max-h-[500px]' : 'max-h-0'}`}
                                        >
                                            <div className="p-4 border-t border-gray-100">
                                                {setupLoading ? (
                                                    <div className="flex justify-center items-center py-4">
                                                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-yellow-400"></div>
                                                        <span className="ml-2 text-gray-600">{t('common.loading', 'Yükleniyor...')}</span>
                                                    </div>
                                                ) : !setupData ? (
                                                    <p className="text-gray-500 text-center py-2">{t('esims.noTechnicalData', 'Teknik bilgiler bulunamadı.')}</p>
                                                ) : (
                                                    <div>
                                                        {!setupData.iccid && !setupData.imsi && !setupData.msisdn && !setupData.apn ? (
                                                            <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-4">
                                                                <div className="flex items-center">
                                                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                                                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                                                    </svg>
                                                                    <p className="text-sm text-yellow-700">{t('esims.technicalDataNotAvailable', 'eSIM teknik bilgileri henüz hazır değil. Lütfen daha sonra tekrar deneyin.')}</p>
                                                                </div>
                                                            </div>
                                                        ) : (
                                                            <div className="space-y-2">
                                                                {setupData.iccid && (
                                                                    <div className="flex justify-between">
                                                                        <span className="text-gray-600">ICCID:</span>
                                                                        <span className="font-mono">{setupData.iccid}</span>
                                                                    </div>
                                                                )}
                                                                {setupData.imsi && (
                                                                    <div className="flex justify-between">
                                                                        <span className="text-gray-600">IMSI:</span>
                                                                        <span className="font-mono">{setupData.imsi}</span>
                                                                    </div>
                                                                )}
                                                                {setupData.msisdn && (
                                                                    <div className="flex justify-between">
                                                                        <span className="text-gray-600">MSISDN:</span>
                                                                        <span className="font-mono">{setupData.msisdn}</span>
                                                                    </div>
                                                                )}
                                                                {setupData.apn && (
                                                                    <div className="flex justify-between">
                                                                        <span className="text-gray-600">APN:</span>
                                                                        <span className="font-mono">{setupData.apn}</span>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        )}
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div className="mt-6 mb-3">
                                    <button className="bg-yellow-400 hover:bg-yellow-500 text-black rounded-lg py-2 px-4 font-medium w-full">
                                        {t('esims.addData', 'Veri Ekle')}
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

export default ESIMsPage;